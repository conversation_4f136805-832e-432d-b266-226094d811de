{"name": "evolution-api", "version": "2.2.3", "description": "Rest api for communication with WhatsApp", "main": "./dist/main.js", "type": "commonjs", "scripts": {"build": "tsc --noEmit && tsup", "start": "tsnd -r tsconfig-paths/register --files --transpile-only ./src/main.ts", "start:prod": "node dist/main", "dev:server": "tsnd -r tsconfig-paths/register --files --transpile-only --respawn --ignore-watch node_modules ./src/main.ts", "test": "tsnd -r tsconfig-paths/register --files --transpile-only --respawn --ignore-watch node_modules ./test/all.test.ts", "lint": "eslint --fix --ext .ts src", "lint:check": "eslint --ext .ts src", "db:generate": "node runWithProvider.js \"npx prisma generate --schema ./prisma/DATABASE_PROVIDER-schema.prisma\"", "db:deploy": "node runWithProvider.js \"rm -rf ./prisma/migrations && cp -r ./prisma/DATABASE_PROVIDER-migrations ./prisma/migrations && npx prisma migrate deploy --schema ./prisma/DATABASE_PROVIDER-schema.prisma\"", "db:deploy:win": "node runWithProvider.js \"xcopy /E /I prisma\\DATABASE_PROVIDER-migrations prisma\\migrations && npx prisma migrate deploy --schema prisma\\DATABASE_PROVIDER-schema.prisma\"", "db:studio": "node runWithProvider.js \"npx prisma studio --schema ./prisma/DATABASE_PROVIDER-schema.prisma\"", "db:migrate:dev": "node runWithProvider.js \"rm -rf ./prisma/migrations && cp -r ./prisma/DATABASE_PROVIDER-migrations ./prisma/migrations && npx prisma migrate dev --schema ./prisma/DATABASE_PROVIDER-schema.prisma && cp -r ./prisma/migrations/* ./prisma/DATABASE_PROVIDER-migrations\"", "db:migrate:dev:win": "node runWithProvider.js \"xcopy /E /I prisma\\DATABASE_PROVIDER-migrations prisma\\migrations && npx prisma migrate dev --schema prisma\\DATABASE_PROVIDER-schema.prisma\""}, "repository": {"type": "git", "url": "git+https://github.com/EvolutionAPI/evolution-api.git"}, "keywords": ["chat", "communication", "message", "send message", "whatsapp", "js-whatsapp", "whatsapp-api", "whatsapp-web", "whatsapp", "whatsapp-chat", "whatsapp-group", "automation", "multi-device", "bot"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "Apache-2.0", "bugs": {"url": "https://github.com/EvolutionAPI/evolution-api/issues"}, "homepage": "https://github.com/EvolutionAPI/evolution-api#readme", "dependencies": {"@adiwajshing/keyed-db": "^0.2.4", "@aws-sdk/client-sqs": "^3.723.0", "@ffmpeg-installer/ffmpeg": "^1.1.0", "@figuro/chatwoot-sdk": "^1.1.16", "@hapi/boom": "^10.0.1", "@paralleldrive/cuid2": "^2.2.2", "@prisma/client": "^6.1.0", "@sentry/node": "^8.47.0", "amqplib": "^0.10.5", "axios": "^1.7.9", "baileys": "github:EvolutionAPI/Baileys", "class-validator": "^0.14.1", "compression": "^1.7.5", "cors": "^2.8.5", "dayjs": "^1.11.13", "dotenv": "^16.4.7", "eventemitter2": "^6.4.9", "express": "^4.21.2", "express-async-errors": "^3.1.1", "fluent-ffmpeg": "^2.1.3", "form-data": "^4.0.1", "https-proxy-agent": "^7.0.6", "i18next": "^23.7.19", "jimp": "^0.16.13", "json-schema": "^0.4.0", "jsonschema": "^1.4.1", "link-preview-js": "^3.0.13", "long": "^5.2.3", "mediainfo.js": "^0.3.4", "mime": "^4.0.0", "mime-types": "^2.1.35", "minio": "^8.0.3", "multer": "^1.4.5-lts.1", "node-cache": "^5.1.2", "node-cron": "^3.0.3", "openai": "^4.77.3", "pg": "^8.13.1", "pino": "^8.11.0", "prisma": "^6.1.0", "pusher": "^5.2.0", "qrcode": "^1.5.4", "qrcode-terminal": "^0.12.0", "redis": "^4.7.0", "sharp": "^0.32.6", "socket.io": "^4.8.1", "socket.io-client": "^4.8.1", "tsup": "^8.3.5"}, "devDependencies": {"@types/compression": "^1.7.5", "@types/cors": "^2.8.17", "@types/express": "^4.17.18", "@types/json-schema": "^7.0.15", "@types/mime": "^4.0.0", "@types/mime-types": "^2.1.4", "@types/node": "^22.10.5", "@types/node-cron": "^3.0.11", "@types/qrcode": "^1.5.5", "@types/qrcode-terminal": "^0.12.2", "@types/uuid": "^10.0.0", "@typescript-eslint/eslint-plugin": "^6.21.0", "@typescript-eslint/parser": "^6.21.0", "eslint": "^8.45.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-import": "^2.31.0", "eslint-plugin-prettier": "^5.2.1", "eslint-plugin-simple-import-sort": "^10.0.0", "prettier": "^3.4.2", "ts-node-dev": "^2.0.0", "tsconfig-paths": "^4.2.0", "typescript": "^5.7.2"}}